'use client';

import React, { useState } from 'react';
import {
  MapPinIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  HomeIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';

interface Address {
  id: string;
  label: string;
  recipientName: string;
  recipientPhone: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  apartmentNumber?: string;
  deliveryInstructions?: string;
  isDefault: boolean;
}

export default function CustomerAddresses() {
  const [addresses] = useState<Address[]>([
    {
      id: '1',
      label: 'Home',
      recipientName: '<PERSON>',
      recipientPhone: '+63 ************',
      street: '123 Main Street',
      city: 'Makati City',
      state: 'Metro Manila',
      zipCode: '1200',
      apartmentNumber: 'Unit 4B',
      deliveryInstructions: 'Ring doorbell twice. Leave at door if no answer.',
      isDefault: true,
    },
    {
      id: '2',
      label: 'Office',
      recipientName: '<PERSON>',
      recipientPhone: '+63 ************',
      street: '456 Business Ave',
      city: 'Taguig City',
      state: 'Metro Manila',
      zipCode: '1634',
      apartmentNumber: 'Suite 200',
      deliveryInstructions: 'Call when arrived. Security desk will direct you.',
      isDefault: false,
    },
  ]);

  const getLabelIcon = (label: string) => {
    switch (label.toLowerCase()) {
      case 'home':
        return <HomeIcon className="h-5 w-5" />;
      case 'office':
      case 'work':
        return <BuildingOfficeIcon className="h-5 w-5" />;
      default:
        return <MapPinIcon className="h-5 w-5" />;
    }
  };

  const formatAddress = (address: Address) => {
    let formatted = address.street;
    if (address.apartmentNumber) formatted += `, ${address.apartmentNumber}`;
    formatted += `, ${address.city}, ${address.state} ${address.zipCode}`;
    return formatted;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Delivery Addresses</h1>
          <p className="text-gray-600">Manage your saved delivery locations.</p>
        </div>
        <button className="flex items-center space-x-2 bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors">
          <PlusIcon className="h-4 w-4" />
          <span>Add Address</span>
        </button>
      </div>

      {/* Addresses List */}
      <div className="space-y-4">
        {addresses.map((address) => (
          <div key={address.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4 flex-1">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center text-orange-600">
                  {getLabelIcon(address.label)}
                </div>

                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="font-semibold text-gray-900">{address.label}</h3>
                    {address.isDefault && (
                      <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium">
                        Default
                      </span>
                    )}
                  </div>

                  <div className="space-y-1 text-sm text-gray-600">
                    <p className="font-medium text-gray-900">{address.recipientName}</p>
                    <p>{formatAddress(address)}</p>
                    <p>{address.recipientPhone}</p>
                    {address.deliveryInstructions && (
                      <p className="text-gray-500">Instructions: {address.deliveryInstructions}</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {!address.isDefault && (
                  <button className="text-orange-600 hover:text-orange-700 text-sm font-medium">
                    Set Default
                  </button>
                )}
                <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                  <PencilIcon className="h-4 w-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100 transition-colors">
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {addresses.length === 0 && (
        <div className="text-center py-12">
          <MapPinIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No addresses saved</h3>
          <p className="text-gray-500 mb-6">Add your first delivery address to get started.</p>
          <button className="inline-block bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
            Add Your First Address
          </button>
        </div>
      )}
    </div>
  );
}
