'use client';

import React, { useState } from 'react';
import {
  QuestionMarkCircleIcon,
  MagnifyingGlassIcon,
  ChevronRightIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  EnvelopeIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

export default function HelpCenter() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  const categories = [
    { id: 'all', name: 'All Topics', count: 12 },
    { id: 'orders', name: 'Orders & Delivery', count: 4 },
    { id: 'payment', name: 'Payment & Billing', count: 3 },
    { id: 'account', name: 'Account & Profile', count: 3 },
    { id: 'technical', name: 'Technical Issues', count: 2 },
  ];

  const faqs: FAQItem[] = [
    {
      id: '1',
      question: 'How can I track my order?',
      answer: 'You can track your order in real-time by going to "Track Order" in your account menu or clicking the tracking link in your order confirmation email. You\'ll see live updates including when your order is being prepared, picked up, and delivered.',
      category: 'orders',
    },
    {
      id: '2',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards (Visa, Mastercard, American Express), PayPal, Apple Pay, Google Pay, and cash on delivery in select areas. You can manage your payment methods in your account settings.',
      category: 'payment',
    },
    {
      id: '3',
      question: 'How do I cancel my order?',
      answer: 'You can cancel your order within 5 minutes of placing it by going to your order history and clicking "Cancel Order". After this window, please contact the restaurant directly or our customer support for assistance.',
      category: 'orders',
    },
    {
      id: '4',
      question: 'Why was my payment declined?',
      answer: 'Payment can be declined for several reasons: insufficient funds, expired card, incorrect billing information, or bank security measures. Please check your payment details and try again, or use a different payment method.',
      category: 'payment',
    },
    {
      id: '5',
      question: 'How do I change my delivery address?',
      answer: 'You can update your delivery address in your account settings under "Addresses". You can also change the address during checkout before confirming your order.',
      category: 'account',
    },
    {
      id: '6',
      question: 'What are the delivery fees?',
      answer: 'Delivery fees vary by restaurant and distance, typically ranging from $1.99 to $4.99. Some restaurants offer free delivery on orders above a certain amount. You\'ll see the exact fee before confirming your order.',
      category: 'orders',
    },
    {
      id: '7',
      question: 'How do I delete my account?',
      answer: 'To delete your account, go to Settings > Security and scroll down to find the "Delete Account" option. Please note that this action is permanent and cannot be undone.',
      category: 'account',
    },
    {
      id: '8',
      question: 'The app is not working properly',
      answer: 'Try these steps: 1) Close and restart the app, 2) Check your internet connection, 3) Update to the latest app version, 4) Clear app cache, 5) Restart your device. If issues persist, contact our support team.',
      category: 'technical',
    },
  ];

  const filteredFAQs = faqs.filter(faq => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Help Center</h1>
        <p className="text-gray-600">Find answers to common questions or get in touch with our support team.</p>
      </div>

      {/* Search */}
      <div className="max-w-2xl mx-auto">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search for help..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow cursor-pointer">
          <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
            <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">Live Chat</h3>
          <p className="text-sm text-gray-600 mb-3">Get instant help from our support team</p>
          <span className="text-sm text-green-600 font-medium">● Available now</span>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow cursor-pointer">
          <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
            <PhoneIcon className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">Call Support</h3>
          <p className="text-sm text-gray-600 mb-3">Speak directly with our team</p>
          <span className="text-sm text-gray-600">+****************</span>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow cursor-pointer">
          <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
            <EnvelopeIcon className="h-6 w-6 text-purple-600" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">Email Support</h3>
          <p className="text-sm text-gray-600 mb-3">Send us a detailed message</p>
          <span className="text-sm text-gray-600">24h response time</span>
        </div>
      </div>

      {/* Categories */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Browse by Category</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`text-left p-4 rounded-lg border transition-colors ${
                selectedCategory === category.id
                  ? 'border-orange-300 bg-orange-50 text-orange-700'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{category.name}</p>
                  <p className="text-sm text-gray-500">{category.count} articles</p>
                </div>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* FAQs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Frequently Asked Questions
          {selectedCategory !== 'all' && (
            <span className="text-sm font-normal text-gray-500 ml-2">
              in {categories.find(c => c.id === selectedCategory)?.name}
            </span>
          )}
        </h3>
        
        <div className="space-y-3">
          {filteredFAQs.map((faq) => (
            <div key={faq.id} className="border border-gray-200 rounded-lg">
              <button
                onClick={() => toggleFAQ(faq.id)}
                className="w-full text-left p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">{faq.question}</h4>
                  <ChevronRightIcon 
                    className={`h-4 w-4 text-gray-400 transition-transform ${
                      expandedFAQ === faq.id ? 'rotate-90' : ''
                    }`} 
                  />
                </div>
              </button>
              {expandedFAQ === faq.id && (
                <div className="px-4 pb-4">
                  <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredFAQs.length === 0 && (
          <div className="text-center py-8">
            <QuestionMarkCircleIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500">
              {searchQuery 
                ? `No results found for "${searchQuery}"`
                : 'No FAQs found in this category.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Support Hours */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div className="flex items-start space-x-3">
          <ClockIcon className="h-6 w-6 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-900 mb-2">Support Hours</h3>
            <div className="text-sm text-blue-800 space-y-1">
              <p><strong>Live Chat:</strong> 24/7 available</p>
              <p><strong>Phone Support:</strong> Monday - Friday, 8 AM - 8 PM EST</p>
              <p><strong>Email Support:</strong> 24/7 (responses within 24 hours)</p>
            </div>
          </div>
        </div>
      </div>

      {/* Still Need Help */}
      <div className="text-center bg-gray-50 rounded-xl p-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Still need help?</h3>
        <p className="text-gray-600 mb-4">
          Can't find what you're looking for? Our support team is here to help.
        </p>
        <button className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
          Contact Support
        </button>
      </div>
    </div>
  );
}
