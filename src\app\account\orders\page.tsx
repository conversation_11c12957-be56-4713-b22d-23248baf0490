'use client';

import React, { useState, useEffect } from 'react';
import {
  ShoppingBagIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  StarIcon,
  EyeIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';

interface OrderItem {
  name: string;
  quantity: number;
  price: number;
  customizations?: string[];
}

interface Order {
  id: string;
  orderNumber: string;
  restaurantName: string;
  restaurantImage: string;
  items: OrderItem[];
  subtotal: number;
  deliveryFee: number;
  tax: number;
  total: number;
  status: 'delivered' | 'preparing' | 'on_the_way' | 'cancelled' | 'pending';
  orderDate: Date;
  deliveryTime?: string;
  deliveryAddress: string;
  paymentMethod: string;
  rating?: number;
  canReorder: boolean;
}

export default function OrderHistory() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState('all');

  useEffect(() => {
    // Simulate loading order history
    setTimeout(() => {
      setOrders([
        {
          id: '1',
          orderNumber: 'ORD-2024-001',
          restaurantName: 'Pizza Palace',
          restaurantImage: '/api/placeholder/60/60',
          items: [
            { name: 'Margherita Pizza', quantity: 1, price: 18.99 },
            { name: 'Garlic Bread', quantity: 2, price: 4.99 },
            { name: 'Coca Cola 500ml', quantity: 1, price: 2.99 },
          ],
          subtotal: 26.97,
          deliveryFee: 2.99,
          tax: 2.40,
          total: 32.36,
          status: 'delivered',
          orderDate: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          deliveryTime: '25 min',
          deliveryAddress: '123 Main St, Apt 4B',
          paymentMethod: 'Credit Card ****1234',
          rating: 5,
          canReorder: true,
        },
        {
          id: '2',
          orderNumber: 'ORD-2024-002',
          restaurantName: 'Burger Junction',
          restaurantImage: '/api/placeholder/60/60',
          items: [
            { name: 'Classic Burger', quantity: 2, price: 12.99, customizations: ['No onions', 'Extra cheese'] },
            { name: 'French Fries', quantity: 1, price: 4.99 },
            { name: 'Milkshake', quantity: 1, price: 5.99 },
          ],
          subtotal: 36.96,
          deliveryFee: 1.99,
          tax: 3.12,
          total: 42.07,
          status: 'delivered',
          orderDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
          deliveryTime: '30 min',
          deliveryAddress: '123 Main St, Apt 4B',
          paymentMethod: 'PayPal',
          rating: 4,
          canReorder: true,
        },
        {
          id: '3',
          orderNumber: 'ORD-2024-003',
          restaurantName: 'Sushi Zen',
          restaurantImage: '/api/placeholder/60/60',
          items: [
            { name: 'California Roll', quantity: 2, price: 8.99 },
            { name: 'Salmon Sashimi', quantity: 1, price: 12.99 },
            { name: 'Miso Soup', quantity: 1, price: 3.99 },
          ],
          subtotal: 34.96,
          deliveryFee: 3.99,
          tax: 3.12,
          total: 42.07,
          status: 'on_the_way',
          orderDate: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
          deliveryTime: '35 min',
          deliveryAddress: '123 Main St, Apt 4B',
          paymentMethod: 'Credit Card ****5678',
          canReorder: false,
        },
        {
          id: '4',
          orderNumber: 'ORD-2024-004',
          restaurantName: 'Taco Fiesta',
          restaurantImage: '/api/placeholder/60/60',
          items: [
            { name: 'Chicken Tacos', quantity: 3, price: 3.99 },
            { name: 'Guacamole', quantity: 1, price: 2.99 },
          ],
          subtotal: 14.96,
          deliveryFee: 1.99,
          tax: 1.36,
          total: 18.31,
          status: 'cancelled',
          orderDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          deliveryAddress: '123 Main St, Apt 4B',
          paymentMethod: 'Credit Card ****1234',
          canReorder: true,
        },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'preparing':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'on_the_way':
        return <TruckIcon className="h-5 w-5 text-blue-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <ShoppingBagIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'preparing':
        return 'bg-yellow-100 text-yellow-800';
      case 'on_the_way':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    
    return date.toLocaleDateString();
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const filteredOrders = orders.filter(order => {
    if (selectedFilter === 'all') return true;
    return order.status === selectedFilter;
  });

  const handleReorder = (orderId: string) => {
    console.log('Reordering:', orderId);
    // Here you would add items to cart and redirect to checkout
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Order History</h1>
          <p className="text-gray-600">Track and manage your past orders.</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
            {orders.length} Total Orders
          </div>
        </div>
      </div>

      {/* Order Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">{orders.length}</p>
            </div>
            <ShoppingBagIcon className="h-8 w-8 text-gray-400" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Delivered</p>
              <p className="text-2xl font-bold text-green-600">
                {orders.filter(o => o.status === 'delivered').length}
              </p>
            </div>
            <CheckCircleIcon className="h-8 w-8 text-green-400" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">In Progress</p>
              <p className="text-2xl font-bold text-blue-600">
                {orders.filter(o => o.status === 'preparing' || o.status === 'on_the_way').length}
              </p>
            </div>
            <TruckIcon className="h-8 w-8 text-blue-400" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Spent</p>
              <p className="text-2xl font-bold text-purple-600">
                ${orders.reduce((sum, order) => sum + order.total, 0).toFixed(2)}
              </p>
            </div>
            <StarIcon className="h-8 w-8 text-purple-400" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-700">Filter by status:</span>
          <div className="flex space-x-2">
            {['all', 'delivered', 'preparing', 'on_the_way', 'cancelled'].map((filter) => (
              <button
                key={filter}
                onClick={() => setSelectedFilter(filter)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  selectedFilter === filter
                    ? 'bg-orange-100 text-orange-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {filter === 'all' ? 'All' : filter.replace('_', ' ').toUpperCase()}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="space-y-4">
        {filteredOrders.map((order) => (
          <div key={order.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                  <ShoppingBagIcon className="h-6 w-6 text-gray-600" />
                </div>
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="font-semibold text-gray-900">#{order.orderNumber}</h3>
                    {getStatusIcon(order.status)}
                  </div>
                  <p className="text-sm text-gray-600">{order.restaurantName}</p>
                  <p className="text-xs text-gray-500">{formatDate(order.orderDate)}</p>
                </div>
              </div>
              <div className="text-right">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                  {order.status.replace('_', ' ').toUpperCase()}
                </span>
                <p className="text-lg font-bold text-gray-900 mt-1">${order.total.toFixed(2)}</p>
              </div>
            </div>

            <div className="border-t border-gray-100 pt-4">
              <div className="space-y-2 mb-4">
                {order.items.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <span className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium">
                        {item.quantity}
                      </span>
                      <span className="text-gray-900">{item.name}</span>
                      {item.customizations && (
                        <span className="text-gray-500">({item.customizations.join(', ')})</span>
                      )}
                    </div>
                    <span className="font-medium text-gray-900">${(item.price * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {order.rating && (
                    <div className="flex items-center space-x-1">
                      {renderStars(order.rating)}
                      <span className="text-sm text-gray-600 ml-1">({order.rating}/5)</span>
                    </div>
                  )}
                  <span className="text-sm text-gray-500">{order.deliveryAddress}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Link
                    href={`/account/orders/${order.id}`}
                    className="flex items-center space-x-1 text-gray-600 hover:text-gray-800 text-sm"
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span>View Details</span>
                  </Link>
                  {order.canReorder && (
                    <button
                      onClick={() => handleReorder(order.id)}
                      className="flex items-center space-x-1 bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
                    >
                      <ArrowPathIcon className="h-4 w-4" />
                      <span>Reorder</span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredOrders.length === 0 && (
        <div className="text-center py-12">
          <ShoppingBagIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
          <p className="text-gray-500 mb-6">
            {selectedFilter === 'all' 
              ? 'You haven\'t placed any orders yet.' 
              : `No orders with status "${selectedFilter.replace('_', ' ')}" found.`}
          </p>
          <Link
            href="/restaurants"
            className="inline-block bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Start Ordering
          </Link>
        </div>
      )}
    </div>
  );
}
