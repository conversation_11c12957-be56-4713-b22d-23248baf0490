'use client';

import React, { useState, useEffect } from 'react';
import {
  ClockIcon,
  CheckCircleIcon,
  TruckIcon,
  MapPinIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  StarIcon,
} from '@heroicons/react/24/outline';

interface TrackingStep {
  id: string;
  title: string;
  description: string;
  timestamp: Date;
  completed: boolean;
  current: boolean;
}

interface ActiveOrder {
  id: string;
  orderNumber: string;
  restaurantName: string;
  restaurantPhone: string;
  driverName?: string;
  driverPhone?: string;
  estimatedDelivery: Date;
  deliveryAddress: string;
  total: number;
  items: Array<{
    name: string;
    quantity: number;
  }>;
  trackingSteps: TrackingStep[];
}

export default function TrackOrder() {
  const [activeOrder, setActiveOrder] = useState<ActiveOrder | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading active order
    setTimeout(() => {
      setActiveOrder({
        id: '1',
        orderNumber: 'ORD-2024-003',
        restaurantName: 'Sushi Zen',
        restaurantPhone: '+****************',
        driverName: '<PERSON>',
        driverPhone: '+****************',
        estimatedDelivery: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes from now
        deliveryAddress: '123 Main St, Apt 4B, New York, NY 10001',
        total: 42.07,
        items: [
          { name: 'California Roll', quantity: 2 },
          { name: 'Salmon Sashimi', quantity: 1 },
          { name: 'Miso Soup', quantity: 1 },
        ],
        trackingSteps: [
          {
            id: '1',
            title: 'Order Placed',
            description: 'Your order has been confirmed',
            timestamp: new Date(Date.now() - 45 * 60 * 1000),
            completed: true,
            current: false,
          },
          {
            id: '2',
            title: 'Restaurant Preparing',
            description: 'The restaurant is preparing your order',
            timestamp: new Date(Date.now() - 35 * 60 * 1000),
            completed: true,
            current: false,
          },
          {
            id: '3',
            title: 'Ready for Pickup',
            description: 'Your order is ready and waiting for driver pickup',
            timestamp: new Date(Date.now() - 10 * 60 * 1000),
            completed: true,
            current: false,
          },
          {
            id: '4',
            title: 'Out for Delivery',
            description: 'Your order is on the way',
            timestamp: new Date(Date.now() - 5 * 60 * 1000),
            completed: true,
            current: true,
          },
          {
            id: '5',
            title: 'Delivered',
            description: 'Your order has been delivered',
            timestamp: new Date(),
            completed: false,
            current: false,
          },
        ],
      });
      setLoading(false);
    }, 1000);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const getTimeRemaining = (estimatedDelivery: Date) => {
    const now = new Date();
    const diffInMinutes = Math.ceil((estimatedDelivery.getTime() - now.getTime()) / (1000 * 60));
    
    if (diffInMinutes <= 0) return 'Arriving now';
    if (diffInMinutes === 1) return '1 minute';
    return `${diffInMinutes} minutes`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
      </div>
    );
  }

  if (!activeOrder) {
    return (
      <div className="text-center py-12">
        <TruckIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Orders</h3>
        <p className="text-gray-500 mb-6">You don't have any orders to track right now.</p>
        <a
          href="/restaurants"
          className="inline-block bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          Start Ordering
        </a>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Track Your Order</h1>
          <p className="text-gray-600">Real-time updates on your delivery.</p>
        </div>
      </div>

      {/* Order Status Card */}
      <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-bold">#{activeOrder.orderNumber}</h2>
            <p className="text-orange-100">{activeOrder.restaurantName}</p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold">${activeOrder.total.toFixed(2)}</p>
            <p className="text-orange-100">{activeOrder.items.length} items</p>
          </div>
        </div>
        
        <div className="bg-white/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/30 rounded-full flex items-center justify-center">
                <TruckIcon className="h-5 w-5" />
              </div>
              <div>
                <p className="font-medium">Estimated Delivery</p>
                <p className="text-sm text-orange-100">
                  {getTimeRemaining(activeOrder.estimatedDelivery)} • {formatTime(activeOrder.estimatedDelivery)}
                </p>
              </div>
            </div>
            <div className="animate-pulse">
              <div className="w-3 h-3 bg-white rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Tracking */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Order Progress</h3>
          
          <div className="space-y-6">
            {activeOrder.trackingSteps.map((step, index) => (
              <div key={step.id} className="flex items-start space-x-4">
                <div className="flex flex-col items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    step.completed 
                      ? 'bg-green-100 text-green-600' 
                      : step.current 
                        ? 'bg-orange-100 text-orange-600' 
                        : 'bg-gray-100 text-gray-400'
                  }`}>
                    {step.completed ? (
                      <CheckCircleIcon className="h-5 w-5" />
                    ) : step.current ? (
                      <ClockIcon className="h-5 w-5" />
                    ) : (
                      <div className="w-3 h-3 rounded-full bg-current"></div>
                    )}
                  </div>
                  {index < activeOrder.trackingSteps.length - 1 && (
                    <div className={`w-0.5 h-8 mt-2 ${
                      step.completed ? 'bg-green-200' : 'bg-gray-200'
                    }`}></div>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className={`font-medium ${
                      step.current ? 'text-orange-600' : step.completed ? 'text-gray-900' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </h4>
                    {step.completed && (
                      <span className="text-sm text-gray-500">{formatTime(step.timestamp)}</span>
                    )}
                  </div>
                  <p className={`text-sm mt-1 ${
                    step.current ? 'text-orange-600' : step.completed ? 'text-gray-600' : 'text-gray-400'
                  }`}>
                    {step.description}
                  </p>
                  {step.current && (
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <div className="animate-pulse w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-sm font-medium text-orange-600">In Progress</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Order Details & Contact */}
        <div className="space-y-6">
          {/* Driver Info */}
          {activeOrder.driverName && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Driver</h3>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-orange-600 font-bold text-lg">
                    {activeOrder.driverName.charAt(0)}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">{activeOrder.driverName}</p>
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: 5 }, (_, i) => (
                      <StarIcon
                        key={i}
                        className={`h-3 w-3 ${i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                      />
                    ))}
                    <span className="text-xs text-gray-500 ml-1">4.8</span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <button className="w-full flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
                  <PhoneIcon className="h-4 w-4" />
                  <span>Call Driver</span>
                </button>
                <button className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                  <ChatBubbleLeftRightIcon className="h-4 w-4" />
                  <span>Message Driver</span>
                </button>
              </div>
            </div>
          )}

          {/* Delivery Address */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Delivery Address</h3>
            <div className="flex items-start space-x-3">
              <MapPinIcon className="h-5 w-5 text-gray-400 mt-0.5" />
              <div>
                <p className="text-gray-900">{activeOrder.deliveryAddress}</p>
                <button className="text-orange-600 hover:text-orange-700 text-sm font-medium mt-1">
                  View on Map
                </button>
              </div>
            </div>
          </div>

          {/* Order Items */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
            <div className="space-y-3">
              {activeOrder.items.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium">
                      {item.quantity}
                    </span>
                    <span className="text-gray-900">{item.name}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Restaurant Contact */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Restaurant</h3>
            <div className="space-y-3">
              <p className="font-medium text-gray-900">{activeOrder.restaurantName}</p>
              <button className="w-full flex items-center justify-center space-x-2 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg transition-colors">
                <PhoneIcon className="h-4 w-4" />
                <span>Call Restaurant</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Live Map Placeholder */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Live Tracking</h3>
        <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <MapPinIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">Interactive map showing driver location</p>
            <p className="text-sm text-gray-400 mt-1">Real-time GPS tracking</p>
          </div>
        </div>
      </div>
    </div>
  );
}
