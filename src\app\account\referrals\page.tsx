'use client';

import React, { useState } from 'react';
import {
  UserPlusIcon,
  GiftIcon,
  ShareIcon,
  DocumentDuplicateIcon,
  CheckCircleIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  LinkIcon,
} from '@heroicons/react/24/outline';

interface Referral {
  id: string;
  name: string;
  email: string;
  status: 'pending' | 'signed_up' | 'first_order' | 'completed';
  dateInvited: Date;
  dateJoined?: Date;
  dateFirstOrder?: Date;
  reward: number;
}

export default function ReferralProgram() {
  const [referralCode] = useState('JOHN2024');
  const [copiedCode, setCopiedCode] = useState(false);
  const [email, setEmail] = useState('');
  const [showInviteForm, setShowInviteForm] = useState(false);

  const [referrals] = useState<Referral[]>([
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'completed',
      dateInvited: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      dateJoined: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000),
      dateFirstOrder: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
      reward: 10,
    },
    {
      id: '2',
      name: 'Mike Chen',
      email: '<EMAIL>',
      status: 'first_order',
      dateInvited: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
      dateJoined: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000),
      dateFirstOrder: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
      reward: 10,
    },
    {
      id: '3',
      name: 'Emily Davis',
      email: '<EMAIL>',
      status: 'signed_up',
      dateInvited: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      dateJoined: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      reward: 5,
    },
    {
      id: '4',
      name: 'Alex Wilson',
      email: '<EMAIL>',
      status: 'pending',
      dateInvited: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      reward: 0,
    },
  ]);

  const copyReferralCode = () => {
    navigator.clipboard.writeText(referralCode);
    setCopiedCode(true);
    setTimeout(() => setCopiedCode(false), 2000);
  };

  const shareReferralLink = (platform: string) => {
    const referralLink = `https://tap2go.com/signup?ref=${referralCode}`;
    const message = `Join Tap2Go and get $5 off your first order! Use my referral code: ${referralCode}`;
    
    switch (platform) {
      case 'email':
        window.open(`mailto:?subject=Join Tap2Go&body=${encodeURIComponent(message + '\n\n' + referralLink)}`);
        break;
      case 'sms':
        window.open(`sms:?body=${encodeURIComponent(message + ' ' + referralLink)}`);
        break;
      case 'copy':
        navigator.clipboard.writeText(referralLink);
        break;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'first_order':
        return 'bg-blue-100 text-blue-800';
      case 'signed_up':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'first_order':
        return 'First Order Placed';
      case 'signed_up':
        return 'Signed Up';
      case 'pending':
        return 'Invitation Sent';
      default:
        return 'Unknown';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const totalEarned = referrals.reduce((sum, ref) => sum + ref.reward, 0);
  const completedReferrals = referrals.filter(ref => ref.status === 'completed').length;
  const pendingReferrals = referrals.filter(ref => ref.status === 'pending').length;

  const handleInviteSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Inviting:', email);
    setEmail('');
    setShowInviteForm(false);
    // Here you would send the invitation
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Refer Friends & Earn</h1>
        <p className="text-gray-600">Share Tap2Go with friends and both of you get rewarded!</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Earned</p>
              <p className="text-3xl font-bold text-green-600">${totalEarned}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <GiftIcon className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Successful Referrals</p>
              <p className="text-3xl font-bold text-blue-600">{completedReferrals}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <CheckCircleIcon className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Pending Invites</p>
              <p className="text-3xl font-bold text-orange-600">{pendingReferrals}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
              <UserPlusIcon className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* How It Works */}
      <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-xl p-6 text-white">
        <h2 className="text-xl font-bold mb-4">How Referrals Work</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mx-auto mb-3">
              <ShareIcon className="h-6 w-6" />
            </div>
            <h3 className="font-semibold mb-2">1. Share Your Code</h3>
            <p className="text-orange-100 text-sm">Send your unique referral code to friends</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mx-auto mb-3">
              <UserPlusIcon className="h-6 w-6" />
            </div>
            <h3 className="font-semibold mb-2">2. Friend Signs Up</h3>
            <p className="text-orange-100 text-sm">They create an account using your code</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mx-auto mb-3">
              <GiftIcon className="h-6 w-6" />
            </div>
            <h3 className="font-semibold mb-2">3. Both Get Rewarded</h3>
            <p className="text-orange-100 text-sm">You both receive $10 credit after their first order</p>
          </div>
        </div>
      </div>

      {/* Referral Code */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Referral Code</h3>
        
        <div className="flex items-center space-x-4 mb-6">
          <div className="flex-1 bg-gray-100 rounded-lg px-4 py-3 flex items-center justify-between">
            <code className="text-xl font-bold text-gray-900">{referralCode}</code>
            <button
              onClick={copyReferralCode}
              className="flex items-center space-x-2 text-orange-600 hover:text-orange-700 font-medium"
            >
              {copiedCode ? (
                <>
                  <CheckCircleIcon className="h-4 w-4" />
                  <span>Copied!</span>
                </>
              ) : (
                <>
                  <DocumentDuplicateIcon className="h-4 w-4" />
                  <span>Copy</span>
                </>
              )}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <button
            onClick={() => shareReferralLink('email')}
            className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg transition-colors"
          >
            <EnvelopeIcon className="h-4 w-4" />
            <span>Share via Email</span>
          </button>
          
          <button
            onClick={() => shareReferralLink('sms')}
            className="flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors"
          >
            <ChatBubbleLeftRightIcon className="h-4 w-4" />
            <span>Share via SMS</span>
          </button>
          
          <button
            onClick={() => shareReferralLink('copy')}
            className="flex items-center justify-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white py-3 px-4 rounded-lg transition-colors"
          >
            <LinkIcon className="h-4 w-4" />
            <span>Copy Link</span>
          </button>
        </div>
      </div>

      {/* Invite Friends */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Invite Friends</h3>
          <button
            onClick={() => setShowInviteForm(!showInviteForm)}
            className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Send Invitation
          </button>
        </div>

        {showInviteForm && (
          <form onSubmit={handleInviteSubmit} className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex space-x-3">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter friend's email address"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
              />
              <button
                type="submit"
                className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Send
              </button>
            </div>
          </form>
        )}

        {/* Referral History */}
        <div className="space-y-3">
          {referrals.map((referral) => (
            <div key={referral.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                  <UserPlusIcon className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">{referral.name}</p>
                  <p className="text-sm text-gray-500">{referral.email}</p>
                  <p className="text-xs text-gray-400">
                    Invited {formatDate(referral.dateInvited)}
                    {referral.dateJoined && ` • Joined ${formatDate(referral.dateJoined)}`}
                    {referral.dateFirstOrder && ` • First order ${formatDate(referral.dateFirstOrder)}`}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(referral.status)}`}>
                  {getStatusText(referral.status)}
                </span>
                {referral.reward > 0 && (
                  <p className="text-sm font-medium text-green-600 mt-1">+${referral.reward}</p>
                )}
              </div>
            </div>
          ))}
        </div>

        {referrals.length === 0 && (
          <div className="text-center py-8">
            <UserPlusIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500">No referrals yet. Start inviting friends!</p>
          </div>
        )}
      </div>

      {/* Terms */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <h3 className="font-medium text-blue-900 mb-3">📋 Referral Terms</h3>
        <div className="text-sm text-blue-800 space-y-2">
          <p>• Both you and your friend receive $10 credit after their first order</p>
          <p>• Your friend gets $5 off their first order when they sign up</p>
          <p>• Credits are applied automatically to your account</p>
          <p>• No limit on the number of friends you can refer</p>
          <p>• Credits expire after 12 months of inactivity</p>
          <p>• Referral rewards cannot be combined with certain promotions</p>
        </div>
      </div>
    </div>
  );
}
